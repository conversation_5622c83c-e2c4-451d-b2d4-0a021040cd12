import type { ModelBase } from './http'
import { useCurdApi } from '@uozi-admin/request'

export interface PricingRule extends ModelBase {
  module: 'llm' | 'tts' | 'asr'
  model_name: string
  unit_price: number
  currency: string
  unit: string
  base_unit: number
  unit_display: string
  is_active: boolean
  priority: number
  description: string
}

export const pricingRuleApi = useCurdApi<PricingRule>('/admin/billing/pricing_rules')
