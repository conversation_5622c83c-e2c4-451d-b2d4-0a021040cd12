<script setup lang="ts">
import type { EasyCurdConfig } from '@billing/curd'
import type { PricingRule } from '@/api/pricing_rule'
import { EasyCurd } from '@billing/curd'
import { pricingRuleApi } from '@/api/pricing_rule'

// 配置 EasyCurd
const config: EasyCurdConfig<PricingRule> = {
  api: pricingRuleApi,
  title: '价格规则管理',
  primaryKey: 'id',

  // 表单字段配置 - 使用字段复用功能，自动生成表格列
  formFields: [
    {
      key: 'module',
      label: '服务类型',
      type: 'select',
      required: true,
      options: [
        { label: 'LLM服务', value: 'llm' },
        { label: 'TTS服务', value: 'tts' },
        { label: 'ASR服务', value: 'asr' },
      ],
    },
    {
      key: 'model_name',
      label: '模型名称',
      type: 'text',
      required: false,
      placeholder: '留空表示通用规则',
    },
    {
      key: 'unit_price',
      label: '单价',
      type: 'number',
      required: true,
      rules: [
        { required: true, message: '单价是必填项' },
        { min: 0, message: '单价不能为负数' },
      ],
    },
    {
      key: 'currency',
      label: '货币',
      type: 'select',
      required: false,
      defaultValue: 'CNY',
      options: [
        { label: '人民币 (CNY)', value: 'CNY' },
        { label: '美元 (USD)', value: 'USD' },
      ],
    },
    {
      key: 'unit',
      label: '计费单位',
      type: 'select',
      required: false,
      defaultValue: 'token',
      options: [
        { label: 'Token', value: 'token' },
        { label: '字符', value: 'character' },
        { label: '秒', value: 'second' },
        { label: '请求', value: 'request' },
      ],
    },
    {
      key: 'base_unit',
      label: '基数单位',
      type: 'select',
      required: false,
      defaultValue: 1,
      options: [
        { label: '1（按个计费）', value: 1 },
        { label: '1,000（按千计费）', value: 1000 },
        { label: '10,000（按万计费）', value: 10000 },
        { label: '100,000（按十万计费）', value: 100000 },
        { label: '1,000,000（按百万计费）', value: 1000000 },
      ],
    },
    {
      key: 'unit_display',
      label: '显示名称',
      type: 'text',
      required: false,
      placeholder: '如：百万tokens、千次调用等',
    },
    {
      key: 'priority',
      label: '优先级',
      type: 'number',
      required: false,
      defaultValue: 1,
      rules: [
        { min: 0, message: '优先级不能为负数' },
      ],
    },
    {
      key: 'is_active',
      label: '启用状态',
      type: 'switch',
      defaultValue: true,
    },
    {
      key: 'description',
      label: '描述',
      type: 'textarea',
      required: false,
      placeholder: '规则描述（可选）',
      col: 2, // 占据2列宽度
    },
  ],

  // 功能开关
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 确认消息
  confirmMessages: {
    delete: '确定要删除这条价格规则吗？删除后将无法恢复！',
    batchDelete: '确定要删除选中的价格规则吗？此操作不可撤销！',
  },

  // 成功消息
  successMessages: {
    create: '价格规则创建成功！',
    update: '价格规则更新成功！',
    delete: '价格规则删除成功！',
    batchDelete: '批量删除完成！',
  },

  // 钩子函数
  hooks: {
    // 创建前处理
    beforeCreate: async (data) => {
      console.log('创建价格规则:', data)
      return data
    },

    // 更新前处理
    beforeUpdate: async (id, data) => {
      console.log('更新价格规则:', id, data)
      return data
    },

    // 删除前确认
    beforeDelete: async (id) => {
      console.log('删除价格规则:', id)
    },

    // 错误处理
    onError: async (error, operation, context) => {
      console.error(`操作 ${operation} 失败:`, error, context)
    },
  },
}

// 获取服务名称
function getServiceName(service: string) {
  const serviceNames = {
    llm: 'LLM服务',
    tts: 'TTS服务',
    asr: 'ASR服务',
  }
  return serviceNames[service as keyof typeof serviceNames] || service
}

// 获取单位名称
function getUnitName(unit: string) {
  const unitNames = {
    token: 'Token',
    character: '字符',
    second: '秒',
    request: '请求',
  }
  return unitNames[unit as keyof typeof unitNames] || unit
}

// 格式化价格显示
function formatPrice(price: number, currency: string) {
  const symbol = currency === 'USD' ? '$' : '¥'
  return `${symbol}${price.toFixed(6)}`
}

// 格式化基数单位显示
function formatBaseUnit(baseUnit: number) {
  if (baseUnit >= 1000000) {
    return `${baseUnit / 1000000}百万`
  }
  else if (baseUnit >= 10000) {
    return `${baseUnit / 10000}万`
  }
  else if (baseUnit >= 1000) {
    return `${baseUnit / 1000}千`
  }
  return baseUnit.toString()
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题和描述 -->
    <div>
      <h1 class="text-2xl font-semibold text-gray-900">
        计费规则配置
      </h1>
    </div>

    <!-- 使用 EasyCurd 组件 -->
    <EasyCurd :config="config">
      <!-- 自定义服务类型列显示 -->
      <template #cell-module="{ value }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {{ getServiceName(value) }}
        </span>
      </template>

      <!-- 自定义模型名称列显示 -->
      <template #cell-model_name="{ value, row }">
        <div>
          <div class="text-sm font-medium text-gray-900">
            {{ value || '通用规则' }}
          </div>
          <div
            v-if="row.description"
            class="text-xs text-gray-500 truncate max-w-xs"
            :title="row.description"
          >
            {{ row.description }}
          </div>
        </div>
      </template>

      <!-- 自定义单价列显示 -->
      <template #cell-unit_price="{ value, row }">
        <div>
          <div class="text-sm font-medium text-gray-900">
            {{ formatPrice(value, row.currency) }}
          </div>
          <div class="text-xs text-gray-500">
            每{{ row.unit_display || getUnitName(row.unit) }}
          </div>
          <div
            v-if="row.base_unit && row.base_unit > 1"
            class="text-xs text-blue-600"
          >
            基数: {{ formatBaseUnit(row.base_unit) }}
          </div>
        </div>
      </template>

      <!-- 自定义计费单位列显示 -->
      <template #cell-unit="{ value }">
        <span class="text-sm text-gray-900">
          {{ getUnitName(value) }}
        </span>
      </template>

      <!-- 自定义优先级列显示 -->
      <template #cell-priority="{ value }">
        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
          {{ value }}
        </span>
      </template>

      <!-- 自定义状态列显示 -->
      <template #cell-is_active="{ value }">
        <div class="flex items-center">
          <div
            class="w-2 h-2 rounded-full mr-2"
            :class="value ? 'bg-green-400' : 'bg-red-400'"
          />
          <span
            class="text-xs font-medium"
            :class="value ? 'text-green-800' : 'text-red-800'"
          >
            {{ value ? '启用' : '禁用' }}
          </span>
        </div>
      </template>
    </EasyCurd>
  </div>
</template>
