package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// PricingRule 价格规则表
type PricingRule struct {
	Model
	Module      string  `json:"module" gorm:"not null;type:varchar(50)" cosy:"add:required;list:in"`                      // 'llm' | 'tts' | 'asr'
	ModelName   string  `json:"model_name" gorm:"type:varchar(100)" cosy:"all:omitempty;list:fussy"`                      // 模型名称，可为空表示通用规则
	UnitPrice   float64 `json:"unit_price" gorm:"type:decimal(10,6);not null" cosy:"add:required,min=0;update:omitempty"` // 单价
	Currency    string  `json:"currency" gorm:"default:'CNY';type:varchar(10)" cosy:"all:omitempty"`                      // 货币类型
	Unit        string  `json:"unit" gorm:"default:'token';type:varchar(20)" cosy:"all:omitempty"`                        // 计费单位
	IsActive    bool    `json:"is_active" gorm:"default:true" cosy:"all:omitempty"`                                       // 是否激活
	Priority    int     `json:"priority" gorm:"default:0" cosy:"all:omitempty"`                                           // 优先级，数字越大优先级越高
	Description string  `json:"description" gorm:"type:text" cosy:"all:omitempty"`                                        // 规则描述
}

// BeforeCreate 创建前生成ID
func (pr *PricingRule) BeforeCreate(_ *gorm.DB) error {
	if pr.ID == 0 {
		pr.ID = sonyflake.NextID()
	}
	return nil
}
